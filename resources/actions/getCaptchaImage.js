// Get balance
import chalk from "chalk";
import axios from "axios";
import useStore from "../stores.js";

export default async function getCaptchaImage(
  request,
  reply,
  browser,
  page,
  event,
  body,
  username,
  password,
) {
  console.log(chalk.red("Get captcha image:", chalk.green(event.request.url)));

  const logError = (message) => {
    console.log(chalk.red("getCaptchaImage Error:", message));
  };

  if (body.hasOwnProperty("imageString")) {
    console.log(chalk.green("Found captcha data, solving..."));

    // Solve captcha here
    const captcha = await axios
      .post("http://157.10.53.230:8080/mbbank", {
        base64: body.imageString,
      })
      .catch(async (e) => {
        console.log(chalk.red("Error:", e.message));
        logError("Cannot solve captcha!");
        return;
      });

    if (!captcha || !captcha?.data) {
      logError("Cannot solve captcha!");
      return;
    }

    // console.log(chalk.green("Captcha resolved:", chalk.yellow(captcha.data)));

    // Fill username, password and captcha

    try {
      // Clear and fill username
      const usernameInput = await page.$('::-p-xpath(//*[@id="user-id"])');
      if (usernameInput) {
        await usernameInput.click({ clickCount: 3 }); // Select all text
        await usernameInput.type(username);
        console.log(chalk.green("Username filled successfully"));
      } else {
        logError("Cannot find username input!");
        return;
      }
    } catch (e) {
      console.log(chalk.red("Error filling username:", e.message));
      logError("Cannot find username input!");
      return;
    }

    try {
      // Clear and fill password
      const passwordInput = await page.$('::-p-xpath(//*[@id="new-password"])');
      if (passwordInput) {
        await passwordInput.click({ clickCount: 3 }); // Select all text
        await passwordInput.type(password);
        console.log(chalk.green("Password filled successfully"));
      } else {
        logError("Cannot find password input!");
        return;
      }
    } catch (e) {
      console.log(chalk.red("Error filling password:", e.message));
      logError("Cannot find password input!");
      return;
    }

    try {
      // Clear and fill captcha
      const captchaInput = await page.$(
        '::-p-xpath(//*[@id="form1"]/div/div[5]/mbb-word-captcha/div/div[2]/div[1]/div[2]/input)',
      );
      if (captchaInput) {
        await captchaInput.click({ clickCount: 3 }); // Select all text
        await captchaInput.type(captcha.data);
        console.log(chalk.green("Captcha filled successfully:", captcha.data));
      } else {
        logError("Cannot find captcha input!");
        return;
      }
    } catch (e) {
      console.log(chalk.red("Error filling captcha:", e.message));
      logError("Cannot find captcha input!");
      return;
    }

    // Wait and verify all fields are filled
    await new Promise((r) => setTimeout(r, 2000));

    // Verify form is filled correctly
    try {
      const usernameValue = await page.$eval('#user-id', el => el.value);
      const passwordValue = await page.$eval('#new-password', el => el.value);
      const captchaValue = await page.$eval('::-p-xpath(//*[@id="form1"]/div/div[5]/mbb-word-captcha/div/div[2]/div[1]/div[2]/input)', el => el.value);

      console.log(chalk.yellow("Form verification:"));
      console.log(chalk.yellow(`- Username filled: ${usernameValue ? 'Yes' : 'No'}`));
      console.log(chalk.yellow(`- Password filled: ${passwordValue ? 'Yes' : 'No'}`));
      console.log(chalk.yellow(`- Captcha filled: ${captchaValue ? 'Yes' : 'No'}`));

      if (!usernameValue || !passwordValue || !captchaValue) {
        logError("Form not filled completely!");
        return;
      }
    } catch (e) {
      console.log(chalk.red("Error verifying form:", e.message));
    }

    // await page.keyboard.press("Tab");

    // await page.keyboard.press("Enter");
    // await new Promise((r) => setTimeout(r, 3000));
    //
    // await page.keyboard.press("Enter");
    // await new Promise((r) => setTimeout(r, 3000));
    //
    // await page.keyboard.press("Enter");

    let clickedCount = 0;

    const setIntervalId = setInterval(async () => {
      clickedCount++;

      if (clickedCount > 10) {
        clearInterval(setIntervalId);
        logError("Cannot click button to login after 10 attempts!");
        return;
      }

      try {
        // Try multiple selectors for login button
        const selectors = [
          '::-p-xpath(//*[@id="login-btn"])',
          '::-p-xpath(//button[contains(@class, "login")])',
          '::-p-xpath(//button[contains(text(), "Đăng nhập")])',
          '::-p-xpath(//input[@type="submit"])',
          'button[type="submit"]',
          '#login-btn',
          '.login-btn',
          'button.btn-primary'
        ];

        let loginButton = null;
        let usedSelector = '';

        for (const selector of selectors) {
          try {
            loginButton = await page.$(selector, { timeout: 2000 });
            if (loginButton) {
              usedSelector = selector;
              break;
            }
          } catch (e) {
            // Continue to next selector
          }
        }

        if (loginButton) {
          // Check if button is visible and enabled
          const isVisible = await loginButton.isIntersectingViewport();
          const isEnabled = await page.evaluate(el => !el.disabled, loginButton);

          console.log(chalk.green(`Found login button with selector: ${usedSelector}`));
          console.log(chalk.yellow(`Button visible: ${isVisible}, enabled: ${isEnabled}`));

          if (isVisible && isEnabled) {
            console.log(chalk.green("Click loginButton"));
            await loginButton.click();

            // Wait a bit to see if login proceeds
            await new Promise(r => setTimeout(r, 1000));

            // Check if we're still on login page or moved to next step
            const currentUrl = page.url();
            if (!currentUrl.includes('/login')) {
              console.log(chalk.green("Login successful, moved to next page"));
              clearInterval(setIntervalId);
              return;
            }
          } else {
            console.log(chalk.yellow(`Button not ready - visible: ${isVisible}, enabled: ${isEnabled}`));
          }
        } else {
          console.log(chalk.yellow(`Attempt ${clickedCount}: Login button not found with any selector`));
        }
      } catch (e) {
        console.log(chalk.red(`Attempt ${clickedCount} error:`, e.message));
      }
    }, 2000);

    // Add interval to store for cleanup
    useStore.getState().addInterval(setIntervalId);
  }
}
