// Get balance
import chalk from "chalk";
import useStore from "../stores.js";

export default async function doLogin(
  request,
  reply,
  browser,
  page,
  event,
  body,
  number,
) {
  console.log(chalk.red("doLogin:", chalk.green(event.request.url)));
  const store = useStore.getState();

  // Debug: Log the response body structure
  console.log(chalk.yellow("doLogin response body keys:"), Object.keys(body));

  // Check for error messages in response
  if (body.hasOwnProperty("result") && body.result) {
    if (body.result.responseCode && body.result.responseCode !== "00") {
      console.log(chalk.red("Lo<PERSON> failed with response code:", body.result.responseCode));
      console.log(chalk.red("Error message:", body.result.message || "Unknown error"));
      return;
    }
  }

  // Wrong account or password
  if (!body.hasOwnProperty("cust") || !body.cust) {
    console.log(chalk.red("Error with account information!"));
    console.log(chalk.yellow("Response body:"), JSON.stringify(body, null, 2));
    return;
  }

  // Get account number
  let foundAccount = null;

  console.log(chalk.yellow("Looking for account number:"), number);
  console.log(chalk.yellow("Available accounts:"), Object.keys(body.cust.acct_list));

  Object.entries(body.cust.acct_list).forEach(([key, account]) => {
    console.log(chalk.cyan(`Checking account key: ${key} against requested: ${number}`));
    if (key === number) {
      foundAccount = account;
      console.log(chalk.green("Found matching account:"), account);
    }
  });

  if (!foundAccount) {
    console.log(chalk.red("No account found!"));
    console.log(chalk.yellow("Requested account number:"), number);
    console.log(chalk.yellow("Available account numbers:"), Object.keys(body.cust.acct_list));
    return;
  }

  console.log(chalk.green("Login successful! Setting session data..."));
  console.log(chalk.yellow("Session ID:"), body.sessionId);
  console.log(chalk.yellow("Device ID:"), body.cust.deviceId);

  store.setSessionId(body.sessionId);
  store.setDeviceId(body.cust.deviceId);

  console.log(chalk.green("Session data set successfully"));
}
