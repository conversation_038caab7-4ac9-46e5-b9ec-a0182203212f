import chalk from "chalk";

/**
 * Safely close browser with timeout and force kill if needed
 * @param {Object} browser - Puppeteer browser instance
 * @param {number} timeout - Timeout in milliseconds (default: 5000)
 * @returns {Promise<void>}
 */
export async function safeBrowserClose(browser, timeout = 5000) {
    if (!browser) {
        return;
    }

    try {
        // Force close all pages first
        const pages = await browser.pages();
        await Promise.all(pages.map(page => page.close().catch(() => {})));
        
        // Close browser with timeout
        await Promise.race([
            browser.close(),
            new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Browser close timeout')), timeout)
            )
        ]);
        
        console.log(chalk.green("Browser closed successfully"));
    } catch (e) {
        console.log(chalk.yellow("Warning: Browser close error:", e.message));
        
        // Force kill browser process if close fails
        try {
            const process = browser.process();
            if (process) {
                process.kill('SIGKILL');
                console.log(chalk.yellow("Browser process killed forcefully"));
            }
        } catch (killError) {
            console.log(chalk.yellow("Warning: Browser kill error:", killError.message));
        }
    }
}

/**
 * Get browser launch options based on environment
 * @param {string} username - Username for userDataDir
 * @returns {Object} Browser launch options
 */
export function getBrowserOptions(username) {
    // Detect if running on VPS/Ubuntu (no display)
    const isHeadless = process.env.DISPLAY === undefined || process.env.NODE_ENV === 'production';
    
    return {
        headless: isHeadless,
        defaultViewport: null,
        args: [
            `--window-size=1920,1000`, 
            `--no-sandbox`,
            `--disable-setuid-sandbox`,
            `--disable-dev-shm-usage`,
            `--disable-gpu`,
            `--no-first-run`,
            `--no-zygote`,
            `--single-process`
        ],
        protocolTimeout: 60000,
        userDataDir: `./users/${username}`,
    };
}
