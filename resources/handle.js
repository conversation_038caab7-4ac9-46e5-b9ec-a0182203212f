// For adBlocker
import chalk from "chalk";
import puppeteer from "puppeteer";
import getBalance from "./actions/getBalance.js";
import doLogin from "./actions/doLogin.js";
import getCaptchaImage from "./actions/getCaptchaImage.js";
import useStore from "./stores.js";
import zlib from 'zlib';
import { safeBrowserClose, getBrowserOptions } from "./utils/browserUtils.js";

export default async (request, reply) => {
  let responseSent = false;

  const sendResponse = (statusCode, data) => {
    if (!responseSent) {
      responseSent = true;
      return reply.code(statusCode).send(data);
    }
  };

  try {
    const { username, password, number } = request.body;
    console.log(chalk.green("IP:", chalk.red(request.ip)));
    console.log(chalk.green("Username:", chalk.yellow(username)));

    // Reset store for new request
    useStore.getState().reset();

    let browser;

    try {
      browser = await puppeteer.launch(getBrowserOptions(username));
    } catch (e) {
      return sendResponse(400, { success: false, message: "Cannot launch browser instance!" });
    }

    // Close all opened pages
    const pages = await browser.pages();
    for (const page of pages) {
      await page.close();
    }

    const page = await browser.newPage();
    let client = await page.createCDPSession();

    await client.send("Fetch.enable", {
      patterns: [
        { resourceType: "Document" },
        {
          urlPattern: "*",
          // resourceType: "Document",
          resourceType: "XHR",
          // requestStage: "Request",
          requestStage: "Response",
        },
      ],
    });

    client.on("Fetch.requestPaused", async (event) => {
      let { requestId } = event;

      if (
        [
          "https://online.mbbank.com.vn/api/retail-web-internetbankingms/getCaptchaImage",
          "https://online.mbbank.com.vn/api/retail_web/internetbanking/v2.0/doLogin",
          "https://online.mbbank.com.vn/api/retail-web-accountms/getBalance",
        ].includes(event.request.url)
      ) {
        let responseData = null;

        try {
          responseData = await client.send("Fetch.getResponseBody", {
            requestId,
          });
        } catch (e) {}

        if (!responseData || !responseData.body) {
          await safeBrowserClose(browser);
          return sendResponse(400, { success: false, message: "No body data found!" });
        }

        let buff = Buffer.from(
          responseData.body,
          responseData.base64Encoded ? "base64" : "utf8",
        );

        // Wait for decompression if needed
        if (responseData.base64Encoded) {
          try {
            const decodedData = await new Promise((resolve, reject) => {
              zlib.gunzip(buff, (err, result) => {
                if (err) reject(err);
                else resolve(result);
              });
            });
            buff = decodedData.toString('utf-8');
          } catch (err) {
            console.log(chalk.red("Error decompressing data maybe it is not compressed: ", err.message));
          }
        } else {
          // Convert buffer to string if not base64 encoded
          buff = buff.toString('utf-8');
        }


        try {
          const body = JSON.parse(buff);

          // console.log(body)

          if (
            event.request.url ===
            "https://online.mbbank.com.vn/api/retail-web-internetbankingms/getCaptchaImage"
          ) {
            getCaptchaImage(
              request,
              reply,
              browser,
              page,
              event,
              body,
              username,
              password,
            );
          }

          if (
            event.request.url ===
            "https://online.mbbank.com.vn/api/retail_web/internetbanking/v2.0/doLogin"
          ) {
            doLogin(request, reply, browser, page, event, body, number);
          }

          if (
            event.request.url ===
            "https://online.mbbank.com.vn/api/retail-web-accountms/getBalance"
          ) {
            getBalance(
              request,
              reply,
              browser,
              page,
              event,
              body,
              username,
              number,
            );
          }
        } catch (e) {
          console.log(
            chalk.red("Error getCaptchaImage doLogin getBalance: ", e.message),
          );
          await safeBrowserClose(browser);
          return sendResponse(400, { success: false, message: "No body data found!" });
        }
      }

      try {
        await client.send("Fetch.continueRequest", { requestId });
      } catch (e) {
        console.log(chalk.red("Error: ", e.message));
      }
    });

    let navigationSuccessful = true;

    try {
      // Try to navigate to login page with increased timeout
      await page.goto("https://online.mbbank.com.vn/pl/login", {
        timeout: 60000,
      });
      console.log(chalk.green("Navigation to login page successful"));
    } catch (navigationError) {
      console.log(chalk.yellow("Navigation timeout, but continuing with login process..."));
      console.log(chalk.yellow("Navigation error:", navigationError.message));
      navigationSuccessful = false;
      // Don't return here - continue with the login process as requests might still work
    }

    try {
      // Wait for the process to complete (max 30 seconds)
      const maxWaitTime = 30000; // 30 seconds for faster timeout
      const checkInterval = 1000; // 1 second
      let waitedTime = 0;

      console.log(chalk.yellow("Waiting for login process to complete..."));

      while (waitedTime < maxWaitTime) {
        const store = useStore.getState();

        // Log progress every 10 seconds
        if (waitedTime % 10000 === 0 && waitedTime > 0) {
          console.log(chalk.cyan(`Waiting... ${waitedTime/1000}s elapsed. Store state:`));
          console.log(chalk.cyan(`- SessionId: ${store.sessionId ? 'Set' : 'Not set'}`));
          console.log(chalk.cyan(`- DeviceId: ${store.deviceId ? 'Set' : 'Not set'}`));
          console.log(chalk.cyan(`- Account: ${Object.keys(store.account).length > 0 ? 'Set' : 'Not set'}`));
          console.log(chalk.cyan(`- Transactions: ${store.transactions.length} items`));
          console.log(chalk.cyan(`- Completed: ${store.completed}`));
        }

        if (store.completed) {
          // Process completed successfully
          console.log(chalk.green("Process completed successfully!"));
          // Clear all intervals before closing browser
          store.clearAllIntervals();
          await safeBrowserClose(browser);

          return sendResponse(200, {
            success: true,
            account: store.account,
            transactions: store.transactions,
            deviceIdCommon: store.deviceId,
            sessionId: store.sessionId,
          });
        }

        await new Promise(resolve => setTimeout(resolve, checkInterval));
        waitedTime += checkInterval;
      }

      // Timeout - process didn't complete in time
      const finalStore = useStore.getState();
      console.log(chalk.yellow("Process timeout after 30s! Checking if we have partial data..."));
      console.log(chalk.yellow(`- SessionId: ${finalStore.sessionId ? 'Set' : 'Not set'}`));
      console.log(chalk.yellow(`- DeviceId: ${finalStore.deviceId ? 'Set' : 'Not set'}`));
      console.log(chalk.yellow(`- Account: ${Object.keys(finalStore.account).length > 0 ? 'Set' : 'Not set'}`));
      console.log(chalk.yellow(`- Transactions: ${finalStore.transactions.length} items`));
      console.log(chalk.yellow(`- Completed: ${finalStore.completed}`));

      // Clear all intervals before closing browser
      finalStore.clearAllIntervals();
      await safeBrowserClose(browser);

      // Check if we have enough data to return success even with timeout
      const hasSessionData = finalStore.sessionId && finalStore.deviceId;
      const hasAccountData = Object.keys(finalStore.account).length > 0;

      if (hasSessionData && hasAccountData) {
        console.log(chalk.green("Timeout but we have sufficient data - returning success"));
        return sendResponse(200, {
          success: true,
          account: finalStore.account,
          transactions: finalStore.transactions,
          deviceIdCommon: finalStore.deviceId,
          sessionId: finalStore.sessionId,
          note: "Completed with timeout but data retrieved successfully"
        });
      }

      // If we don't have sufficient data, return timeout error
      console.log(chalk.red("Timeout and insufficient data - returning error"));
      return sendResponse(400, {
        success: false,
        message: "Process timeout - login flow did not complete in time",
        debug: {
          navigationSuccessful,
          sessionId: finalStore.sessionId ? 'Set' : 'Not set',
          deviceId: finalStore.deviceId ? 'Set' : 'Not set',
          accountSet: Object.keys(finalStore.account).length > 0,
          transactionCount: finalStore.transactions.length,
          completed: finalStore.completed
        }
      });

    } catch (e) {
      // Clear all intervals before closing browser
      useStore.getState().clearAllIntervals();
      await safeBrowserClose(browser);

      // Don't return navigation timeout as error since login process might still work
      if (e.message && e.message.includes('Navigation timeout')) {
        return sendResponse(400, {
          success: false,
          message: "Login process timeout - please try again",
          debug: {
            originalError: e.message,
            navigationSuccessful: false
          }
        });
      }

      return sendResponse(400, {
        success: false,
        message: `Cannot open bank app, unexpected error: ${e.message}`,
      });
    }
  } catch (e) {
    return sendResponse(400, {
      success: false,
      message: e.message ?? "Cannot launch browser instance!",
    });
  }
};
