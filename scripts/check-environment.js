#!/usr/bin/env node

import chalk from "chalk";
import puppeteer from "puppeteer";
import { getBrowserOptions } from "../resources/utils/browserUtils.js";

console.log(chalk.blue("=== Environment Check ==="));

// Check environment variables
console.log(chalk.yellow("Environment Variables:"));
console.log(`- NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
console.log(`- DISPLAY: ${process.env.DISPLAY || 'undefined'}`);
console.log(`- Platform: ${process.platform}`);
console.log(`- Architecture: ${process.arch}`);

// Check if running in headless mode
const isHeadless = process.env.DISPLAY === undefined || process.env.NODE_ENV === 'production';
console.log(`- Will run headless: ${isHeadless}`);

console.log(chalk.yellow("\nBrowser Options:"));
const browserOptions = getBrowserOptions("test-user");
console.log(JSON.stringify(browserOptions, null, 2));

console.log(chalk.yellow("\nTesting browser launch..."));

try {
    const browser = await puppeteer.launch(browserOptions);
    console.log(chalk.green("✓ Browser launched successfully"));
    
    const pages = await browser.pages();
    console.log(`✓ Initial pages: ${pages.length}`);
    
    const page = await browser.newPage();
    console.log("✓ New page created");
    
    await page.goto('https://example.com', { timeout: 10000 });
    console.log("✓ Page navigation successful");
    
    await browser.close();
    console.log(chalk.green("✓ Browser closed successfully"));
    
    console.log(chalk.green("\n=== All checks passed! ==="));
} catch (error) {
    console.log(chalk.red("✗ Browser test failed:"), error.message);
    console.log(chalk.yellow("\nTroubleshooting tips:"));
    console.log("1. Install required dependencies:");
    console.log("   sudo apt-get update");
    console.log("   sudo apt-get install -y gconf-service libasound2-dev libatk1.0-dev libc6-dev libcairo2-dev libcups2-dev libdbus-1-dev libexpat1-dev libfontconfig1-dev libgcc1 libgconf-2-4 libgdk-pixbuf2.0-dev libglib2.0-dev libgtk-3-dev libnspr4-dev libpango-1.0-dev libpangocairo-1.0-dev libstdc++6 libx11-dev libx11-xcb-dev libxcb1-dev libxcomposite-dev libxcursor-dev libxdamage-dev libxext-dev libxfixes-dev libxi-dev libxrandr-dev libxrender-dev libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3-dev lsb-release xdg-utils wget");
    console.log("2. Set NODE_ENV=production for VPS deployment");
    console.log("3. Ensure no DISPLAY variable is set for headless mode");
    
    process.exit(1);
}
