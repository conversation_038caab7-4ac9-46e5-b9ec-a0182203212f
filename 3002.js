import Fastify from "fastify";
import Handle from "./resources/handle.js";

(async () => {
  const convertTZ = (date, tzString) => {
    return new Date(
      (typeof date === "string" ? new Date(date) : date).toLocaleString("vn", {
        timeZone: tzString,
      }),
    );
  };

  const fastify = Fastify({
    logger: {
      timestamp: () =>
        `,"time":"${convertTZ(
          new Date(),
          "Asia/Ho_Chi_Minh",
        ).toLocaleString()}"`,
    },
  });

  fastify.setErrorHandler((error, request, reply) => {
    // handle timeout exception
    reply
      .code(error.statusCode ?? 500)
      .send({ success: false, message: error.message });
  });

  fastify.post("/mbbank", (request, reply) => {
    Handle(request, reply);
  });

  await fastify.listen({ port: 3002, host: "::" }, function (err, address) {
    if (err) {
      fastify.log.error(err);
      process.exit(1);
    }
    fastify.log.info(`server listening on ${address}`);
  });
})();
