// Test script to debug login issues
import axios from 'axios';
import chalk from 'chalk';

const testLogin = async () => {
  try {
    console.log(chalk.yellow('Testing login with improved error handling...'));
    
    const response = await axios.post('http://localhost:3000/api/mb', {
      username: 'YOUR_USERNAME', // Replace with actual username
      password: 'YOUR_PASSWORD', // Replace with actual password
      number: 'YOUR_ACCOUNT_NUMBER' // Replace with actual account number
    }, {
      timeout: 120000 // 2 minutes timeout
    });
    
    console.log(chalk.green('Success:'), response.data);
  } catch (error) {
    if (error.response) {
      console.log(chalk.red('Error Response:'), error.response.data);
      console.log(chalk.red('Status:'), error.response.status);
    } else if (error.request) {
      console.log(chalk.red('No response received:'), error.request);
    } else {
      console.log(chalk.red('Error:'), error.message);
    }
  }
};

// Run test
testLogin();
