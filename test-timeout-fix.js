#!/usr/bin/env node

import axios from 'axios';
import chalk from 'chalk';

console.log(chalk.blue('=== Testing Timeout Fix ==='));
console.log(chalk.yellow('This test will help verify if the navigation timeout issue is resolved'));
console.log(chalk.cyan('Note: Replace credentials with real ones to test properly\n'));

const testTimeoutFix = async () => {
  let startTime = Date.now();

  try {
    console.log(chalk.yellow('Making request to MB Bank API...'));
    console.log(chalk.cyan('Request started at:', new Date().toLocaleTimeString()));

    const response = await axios.post('http://localhost:3001/mbbank', {
      username: 'test_username', // Replace with actual username
      password: 'test_password', // Replace with actual password
      number: 'test_account'     // Replace with actual account number
    }, {
      timeout: 120000 // 2 minutes timeout
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(chalk.green(`✓ Request completed successfully in ${duration}s`));
    console.log(chalk.green('Response:'), JSON.stringify(response.data, null, 2));

  } catch (error) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(chalk.red(`✗ Request failed after ${duration}s`));

    if (error.response) {
      console.log(chalk.red('Status:'), error.response.status);
      console.log(chalk.red('Error response:'), JSON.stringify(error.response.data, null, 2));

      // Check if this is the old navigation timeout error
      if (error.response.data.message && error.response.data.message.includes('Navigation timeout')) {
        console.log(chalk.yellow('\n⚠️  This appears to be the old navigation timeout error'));
        console.log(chalk.yellow('The fix may not be working as expected'));
      }
    } else {
      console.log(chalk.red('Error:'), error.message);
    }
  }
};

testTimeoutFix();
